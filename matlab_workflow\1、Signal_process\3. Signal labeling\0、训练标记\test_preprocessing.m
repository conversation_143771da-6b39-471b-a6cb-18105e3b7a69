function test_preprocessing()
% TEST_PREPROCESSING 测试音频分段预处理功能
%
% 功能描述：
% 1. 运行音频分段预处理函数
% 2. 验证生成的分段timetable数据格式
% 3. 检查分段索引文件和时间连续性
% 4. 显示分段处理结果的统计信息
% 5. 可视化滤波前后的频谱对比

    % 清理工作环境
    clear all;
    clc;
    close all;

    fprintf('=== 开始测试音频预处理功能 ===\n\n');
    
    %% 运行音频预处理
    try
        fprintf('1. 运行音频预处理函数...\n');
        audio_preprocessing();
        fprintf('音频预处理完成！\n\n');
    catch ME
        fprintf('音频预处理失败: %s\n', ME.message);
        return;
    end
    
    %% 验证生成的分段数据
    fprintf('2. 验证生成的分段数据...\n');

    % 设置文件路径
    currentDir = fileparts(mfilename('fullpath'));
    outputFolder = fullfile(currentDir, '2、Processed data');

    % 获取生成的分段文件
    segmentFiles = dir(fullfile(outputFolder, '*_segment_*_tt.mat'));
    indexFiles = dir(fullfile(outputFolder, '*_segments_index.mat'));
    infoFiles = dir(fullfile(outputFolder, '*_tt_info.mat'));

    if isempty(segmentFiles)
        fprintf('未找到分段处理后的.mat文件\n');
        return;
    end

    fprintf('找到文件:\n');
    fprintf('  分段文件: %d 个\n', length(segmentFiles));
    fprintf('  索引文件: %d 个\n', length(indexFiles));
    fprintf('  信息文件: %d 个\n', length(infoFiles));

    %% 验证分段索引文件
    if ~isempty(indexFiles)
        fprintf('\n验证分段索引文件...\n');
        indexFile = fullfile(outputFolder, indexFiles(1).name);
        fprintf('正在验证索引文件: %s\n', indexFiles(1).name);

        try
            indexData = load(indexFile);
            segmentIndex = indexData.segmentIndex;

            fprintf('索引文件信息:\n');
            fprintf('  原始文件: %s\n', segmentIndex.originalFile);
            fprintf('  总时长: %.2f 秒\n', segmentIndex.totalDuration);
            fprintf('  分段时长: %d 秒\n', segmentIndex.segmentDuration);
            fprintf('  分段数量: %d\n', segmentIndex.totalSegments);
            fprintf('  采样率: %d Hz\n', segmentIndex.samplingRate);
            fprintf('  通道数: %d\n', segmentIndex.numChannels);

            % 验证时间连续性
            fprintf('\n验证时间连续性:\n');
            segments = segmentIndex.segments;
            timeGaps = zeros(1, length(segments)-1); % 预分配数组

            for i = 1:length(segments)-1
                currentEnd = segments(i).endTime;
                nextStart = segments(i+1).startTime;
                gap = nextStart - currentEnd;
                timeGaps(i) = gap;

                if abs(gap) > 1/segmentIndex.samplingRate % 允许一个采样点的误差
                    fprintf('  ⚠️  分段 %d 和 %d 之间存在时间间隙: %.6f 秒\n', i, i+1, gap);
                end
            end

            if all(abs(timeGaps) <= 1/segmentIndex.samplingRate)
                fprintf('  ✓ 所有分段时间连续，无间隙\n');
            end

            % 显示分段详情（前5个）
            fprintf('\n前5个分段详情:\n');
            for i = 1:min(5, length(segments))
                seg = segments(i);
                fprintf('  分段 %d: %.2f-%.2f 秒 (%.2f 秒) - %s\n', ...
                    seg.segmentIndex, seg.startTime, seg.endTime, ...
                    seg.duration, seg.fileName);
            end

        catch ME
            fprintf('验证索引文件时发生错误: %s\n', ME.message);
        end
    end

    %% 验证分段timetable数据
    fprintf('\n验证分段timetable数据...\n');
    testFile = fullfile(outputFolder, segmentFiles(1).name);
    fprintf('正在验证分段文件: %s\n', segmentFiles(1).name);

    try
        % 加载数据
        loadedData = load(testFile);
        fieldNames = fieldnames(loadedData);

        fprintf('分段文件包含的变量:\n');
        for i = 1:length(fieldNames)
            fprintf('  %s\n', fieldNames{i});
        end

        % 检查第一个timetable
        if ~isempty(fieldNames)
            firstTT = loadedData.(fieldNames{1});

            if isa(firstTT, 'timetable')
                fprintf('\n✓ 分段数据格式验证通过 - 是有效的timetable\n');

                % 显示timetable基本信息
                fprintf('分段Timetable信息:\n');
                fprintf('  变量数量: %d\n', width(firstTT));
                fprintf('  时间点数量: %d\n', height(firstTT));
                fprintf('  采样率: %.2f Hz\n', firstTT.Properties.SampleRate);
                fprintf('  分段时长: %.2f 秒\n', seconds(firstTT.Properties.TimeStep * height(firstTT)));

                % 显示数据统计
                dataColumn = firstTT{:,1};
                fprintf('  数据统计:\n');
                fprintf('    最小值: %.6f\n', min(dataColumn));
                fprintf('    最大值: %.6f\n', max(dataColumn));
                fprintf('    均值: %.6f\n', mean(dataColumn));
                fprintf('    标准差: %.6f\n', std(dataColumn));

            else
                fprintf('✗ 分段数据格式错误 - 不是timetable格式\n');
            end
        end

    catch ME
        fprintf('验证分段数据时发生错误: %s\n', ME.message);
    end

    %% 统计所有分段信息
    fprintf('\n统计所有分段信息...\n');

    % 按原始文件分组
    fileGroups = containers.Map();
    for i = 1:length(segmentFiles)
        fileName = segmentFiles(i).name;
        % 提取原始文件名
        baseName = regexprep(fileName, '_segment_\d+_tt\.mat$', '');

        if isKey(fileGroups, baseName)
            fileGroups(baseName) = fileGroups(baseName) + 1;
        else
            fileGroups(baseName) = 1;
        end
    end

    % 显示统计结果
    baseNames = keys(fileGroups);
    for i = 1:length(baseNames)
        baseName = baseNames{i};
        segmentCount = fileGroups(baseName);
        fprintf('  %s: %d 个分段\n', baseName, segmentCount);
    end
    
    %% 可视化分段处理结果
    fprintf('\n3. 生成分段处理可视化结果...\n');

    try
        % 读取原始音频文件进行对比
        inputFolder = fullfile(currentDir, '1、Raw data');
        audioFiles = dir(fullfile(inputFolder, '*.wav'));

        if ~isempty(audioFiles) && ~isempty(indexFiles)
            originalFile = fullfile(inputFolder, audioFiles(1).name);
            [originalAudio, fs] = audioread(originalFile);

            % 如果是多通道，只取第一个通道
            if size(originalAudio, 2) > 1
                originalAudio = originalAudio(:, 1);
            end

            % 应用相同的滤波器
            filteredAudio = bandpass(originalAudio, [100 800], fs);

            % 加载分段索引信息
            indexData = load(fullfile(outputFolder, indexFiles(1).name));
            segmentIndex = indexData.segmentIndex;

            % 创建图形 - 分段可视化
            figure('Name', '音频分段处理结果', 'Position', [100 100 1400 1000]);

            % 1. 原始音频信号（带分段标记）
            subplot(3, 2, 1);
            t = (0:length(originalAudio)-1) / fs;
            plot(t, originalAudio, 'b-', 'LineWidth', 0.5);
            title('原始音频信号（带分段标记）');
            xlabel('时间 (秒)');
            ylabel('幅度');
            grid on;

            % 添加分段分界线
            hold on;
            for i = 1:length(segmentIndex.segments)
                seg = segmentIndex.segments(i);
                xline(seg.startTime, 'g--', 'Alpha', 0.7, 'LineWidth', 1);
                if i == 1
                    xline(seg.endTime, 'r--', 'Alpha', 0.7, 'LineWidth', 1);
                end
            end
            legend('原始信号', '分段起始', '分段结束', 'Location', 'best');

            % 2. 滤波后音频信号（带分段标记）
            subplot(3, 2, 2);
            plot(t, filteredAudio, 'r-', 'LineWidth', 0.5);
            title('滤波后音频信号（带分段标记）');
            xlabel('时间 (秒)');
            ylabel('幅度');
            grid on;

            % 添加分段分界线
            hold on;
            for i = 1:length(segmentIndex.segments)
                seg = segmentIndex.segments(i);
                xline(seg.startTime, 'g--', 'Alpha', 0.7, 'LineWidth', 1);
                if i == 1
                    xline(seg.endTime, 'r--', 'Alpha', 0.7, 'LineWidth', 1);
                end
            end

            % 3. 分段时长分布
            subplot(3, 2, 3);
            segmentDurations = [segmentIndex.segments.duration];
            bar(1:length(segmentDurations), segmentDurations);
            title('各分段时长分布');
            xlabel('分段编号');
            ylabel('时长 (秒)');
            grid on;
            ylim([0, max(segmentDurations) * 1.1]);

            % 添加60秒参考线
            hold on;
            yline(60, 'r--', '60秒参考线', 'LineWidth', 2);

            % 4. 显示第一个分段的详细信息
            if ~isempty(segmentFiles)
                subplot(3, 2, 4);
                firstSegmentFile = fullfile(outputFolder, segmentFiles(1).name);
                segmentData = load(firstSegmentFile);
                fieldNames = fieldnames(segmentData);

                if ~isempty(fieldNames)
                    firstSegmentTT = segmentData.(fieldNames{1});
                    segmentTime = firstSegmentTT.Properties.TimeStep * (0:height(firstSegmentTT)-1)';
                    plot(seconds(segmentTime), firstSegmentTT{:,1}, 'g-', 'LineWidth', 1);
                    title(sprintf('第一个分段详细信号 (%s)', segmentFiles(1).name));
                    xlabel('时间 (秒)');
                    ylabel('幅度');
                    grid on;
                end
            end

            % 5. 频域对比
            subplot(3, 2, 5);
            [pxx_orig, f_orig] = pwelch(originalAudio, [], [], [], fs);
            loglog(f_orig, pxx_orig, 'b-', 'LineWidth', 1.5);
            title('原始音频功率谱密度');
            xlabel('频率 (Hz)');
            ylabel('功率谱密度');
            grid on;
            xlim([1 fs/2]);

            % 添加滤波器频率范围标记
            hold on;
            xline(100, 'g--', 'LineWidth', 2, 'Alpha', 0.7);
            xline(800, 'g--', 'LineWidth', 2, 'Alpha', 0.7);
            legend('原始信号', '100Hz', '800Hz', 'Location', 'best');

            subplot(3, 2, 6);
            [pxx_filt, f_filt] = pwelch(filteredAudio, [], [], [], fs);
            loglog(f_filt, pxx_filt, 'r-', 'LineWidth', 1.5);
            title('滤波后音频功率谱密度');
            xlabel('频率 (Hz)');
            ylabel('功率谱密度');
            grid on;
            xlim([1 fs/2]);

            % 添加滤波器频率范围标记
            hold on;
            xline(100, 'g--', 'LineWidth', 2, 'Alpha', 0.7);
            xline(800, 'g--', 'LineWidth', 2, 'Alpha', 0.7);
            legend('滤波后信号', '100Hz', '800Hz', 'Location', 'best');

            fprintf('✓ 分段处理可视化图形已生成\n');

        else
            fprintf('未找到原始音频文件或索引文件用于可视化\n');
        end
        
    catch ME
        fprintf('生成可视化时发生错误: %s\n', ME.message);
    end
    
    %% 检查标注文件与分段的对应关系
    fprintf('\n4. 检查标注文件与分段的对应关系...\n');

    try
        inputFolder = fullfile(currentDir, '1、Raw data');
        txtFiles = dir(fullfile(inputFolder, '*.txt'));

        if ~isempty(txtFiles) && ~isempty(indexFiles)
            txtFile = fullfile(inputFolder, txtFiles(1).name);
            fprintf('找到标注文件: %s\n', txtFiles(1).name);

            % 读取标注数据
            annotations = readtable(txtFile, 'FileType', 'text', ...
                'Delimiter', '\t', 'ReadVariableNames', false);

            if size(annotations, 2) >= 3
                fprintf('标注文件格式:\n');
                fprintf('  列数: %d\n', size(annotations, 2));
                fprintf('  行数: %d\n', size(annotations, 1));
                fprintf('  标注类别: %s\n', strjoin(unique(string(annotations{:,3})), ', '));

                % 分析标注与分段的对应关系
                indexData = load(fullfile(outputFolder, indexFiles(1).name));
                segmentIndex = indexData.segmentIndex;

                fprintf('\n标注与分段对应分析:\n');
                for segIdx = 1:length(segmentIndex.segments)
                    seg = segmentIndex.segments(segIdx);

                    % 找到该分段内的标注
                    segmentAnnotations = annotations(annotations{:,1} >= seg.startTime & ...
                        annotations{:,2} <= seg.endTime, :);

                    % 找到跨越该分段的标注
                    crossAnnotations = annotations((annotations{:,1} < seg.startTime & ...
                        annotations{:,2} > seg.startTime) | ...
                        (annotations{:,1} < seg.endTime & ...
                        annotations{:,2} > seg.endTime), :);

                    fprintf('  分段 %d (%.2f-%.2f 秒): %d 个完整标注, %d 个跨越标注\n', ...
                        segIdx, seg.startTime, seg.endTime, ...
                        height(segmentAnnotations), height(crossAnnotations));
                end

                % 显示前几行示例
                fprintf('\n前5行标注示例:\n');
                for i = 1:min(5, height(annotations))
                    fprintf('    %.6f\t%.6f\t%s\n', ...
                        annotations{i,1}, annotations{i,2}, string(annotations{i,3}));
                end
            else
                fprintf('标注文件格式可能不正确\n');
            end

        else
            fprintf('未找到标注文件或索引文件\n');
        end

    catch ME
        fprintf('分析标注文件时发生错误: %s\n', ME.message);
    end

    %% 分段处理测试总结
    fprintf('\n=== 分段处理测试完成 ===\n');
    fprintf('✓ 音频分段预处理功能测试完成\n');
    fprintf('✓ 生成的分段timetable数据可用于后续标注函数\n');
    fprintf('✓ 分段索引文件记录了完整的时间对应关系\n');
    fprintf('✓ 时间连续性验证通过\n');

    fprintf('\n下一步建议:\n');
    fprintf('1. 使用生成的分段timetable数据测试标注函数\n');
    fprintf('2. 验证跨分段标注的处理策略\n');
    fprintf('3. 检查60秒分段是否适合标注函数的输入要求\n');
    fprintf('4. 考虑是否需要调整分段长度或重叠处理\n');
    
end
