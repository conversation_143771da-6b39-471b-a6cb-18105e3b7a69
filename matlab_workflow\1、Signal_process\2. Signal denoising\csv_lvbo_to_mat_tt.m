%% CSV信号降噪处理与时间表转换工具
% =========================================================================
% 功能：批量处理CSV文件，进行信号降噪并转换为MATLAB时间表格式
%
% 输入：包含多个CSV文件的文件夹（每个文件至少包含3列数据）
% 输出：在同级目录"2、Processed data"文件夹中生成降噪后的MAT文件（包含时间表）
%
% 处理步骤：
%   1. 选择文件夹并按文件名数字排序
%   2. 提取CSV文件第2、3列数据进行预处理
%   3. 应用100-800Hz带通滤波
%   4. 使用谱减法进一步降噪
%   5. 创建时间表并保存为MAT文件
%
% 应用场景：音频信号处理、生物医学信号降噪、传感器数据预处理
% =========================================================================

clear all;
clc;
close all;

%% 选择文件夹并读取csv文件
% 选择文件夹
folderPath = uigetdir('', '选择文件夹'); % 弹出文件夹选择框
if folderPath == 0
    disp('用户取消了文件夹选择');
    return;
end

% 获取文件夹中的所有csv文件
csvFiles = dir(fullfile(folderPath, '*.csv'));

% 如果没有找到csv文件，退出程序
if isempty(csvFiles)
    disp('文件夹中没有找到csv文件');
    return;
end

% 提取文件名中的数字并排序
fileNumbers = zeros(length(csvFiles), 1);
for i = 1:length(csvFiles)
    % 使用正则表达式提取文件名中的数字
    fileName = csvFiles(i).name;
    numbers = regexp(fileName, '\d+', 'match');
    if ~isempty(numbers)
        fileNumbers(i) = str2double(numbers{1});
    end
end

% 根据提取的数字对文件进行排序
[~, sortIdx] = sort(fileNumbers);
csvFiles = csvFiles(sortIdx);

% 设置采样率
samplingRate = 2570;

%% 遍历每个csv文件
for i = 1:length(csvFiles)
    % 获取当前csv文件的完整路径
    currentFileName = fullfile(folderPath, csvFiles(i).name);
    
    % 读取csv文件
    data = readtable(currentFileName);
    data(1, :) = []; % 删除第一行
    
    % 提取第二列和第三列数据
    column2 = data{:, 2};
    column3 = data{:, 3};
    
    % 生成时间序列
    time = (0:length(column2)-1)' / samplingRate;
    
    % 数据预处理
    [column2, column3] = preprocessData(column2, column3);
    
    %% 降噪
    % 带通滤波
    low_freq = 100; % 带通滤波的低频边界
    high_freq = 800; % 带通滤波的高频边界
    column2_filtered = bandpass(column2, [low_freq high_freq], samplingRate);
    column3_filtered = bandpass(column3, [low_freq high_freq], samplingRate);
    
    % 计算带通滤波后的SNR
    snr_column2 = SNR_singlech(column2_filtered, column2);
    snr_column3 = SNR_singlech(column3_filtered, column3);
    fprintf('带通滤波后 column2 的 SNR = %.4f dB\n', snr_column2);
    fprintf('带通滤波后 column3 的 SNR = %.4f dB\n', snr_column3);
    
    % 谱减法降噪处理
    % 对 column2_filtered 进行谱减法处理
    x = column2_filtered;
    N = length(x); % 信号长度
    IS = 0.15; % 设置初始静音段比例
    
    % 谱减滤波
    output = SSBoll79(x, samplingRate, IS); % 调用谱减函数
    ol = length(output);
    if ol < N
        output = [output; zeros(N - ol, 1)]; % 补零使长度一致
    end
    
    % 计算谱减后的SNR
    snr_column2_ss = SNR_singlech(output, x);
    fprintf('谱减滤波后 column2 的 SNR = %.4f dB\n', snr_column2_ss);
    
    % 更新 column2
    column2 = output;
    
    % 对 column3_filtered 进行谱减法处理
    x = column3_filtered;
    N = length(x);
    output = SSBoll79(x, samplingRate, IS);
    ol = length(output);
    if ol < N
        output = [output; zeros(N - ol, 1)];
    end
    snr_column3_ss = SNR_singlech(output, x);
    fprintf('谱减滤波后 column3 的 SNR = %.4f dB\n', snr_column3_ss);
    
    % 更新 column3
    column3 = output;
    
    %% 创建时间表
    tt1 = timetable(column2, 'SampleRate', samplingRate);
    tt2 = timetable(column3, 'SampleRate', samplingRate);
    
    % 创建输出文件夹，如果不存在的话
    outputFolder = fullfile(fileparts(mfilename('fullpath')), '2、Processed data');
    if ~exist(outputFolder, 'dir')
        mkdir(outputFolder);
    end
    
    % 生成新文件名（加上 "_tt" 后缀）
    [~, name, ~] = fileparts(currentFileName);
    newFileName = fullfile(outputFolder, [name, '_tt.mat']);
    
    % 保存时间表数据为mat文件
    save(newFileName, 'tt1', 'tt2');
    
    % 输出处理进度
    disp(['文件处理完成: ', newFileName]);
end