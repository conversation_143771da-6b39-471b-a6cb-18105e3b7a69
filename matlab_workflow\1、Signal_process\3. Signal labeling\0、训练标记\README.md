# 音频分段预处理工具使用说明

## 概述
本工具用于处理肠鸣音音频文件，包括带通滤波、60秒分段处理和格式转换功能，为后续的标注函数提供标准化的分段输入数据。

## 文件结构
```
0、训练标记/
├── 1、Raw data/           # 原始音频和标注文件
│   ├── *.wav             # 音频文件
│   └── *.txt             # 标注文件
├── 2、Processed data/     # 处理后的数据（自动创建）
├── audio_preprocessing.m  # 主要预处理函数
├── test_preprocessing.m   # 测试和验证脚本
└── README.md             # 本说明文件
```

## 功能特性

### 1. 音频分段预处理 (`audio_preprocessing.m`)
- **自动文件检测**: 支持 .wav, .mp3, .m4a, .flac 格式
- **带通滤波**: 100-800Hz 频率范围，去除噪声
- **分段处理**: 将长音频按60秒自动分段，便于后续处理
- **多通道支持**: 自动检测并处理多通道音频
- **格式转换**: 每个分段转换为独立的 timetable 格式文件
- **索引管理**: 生成分段索引文件记录时间对应关系
- **错误处理**: 完善的错误处理和日志输出

### 2. 分段数据验证 (`test_preprocessing.m`)
- **分段格式验证**: 检查生成的分段 timetable 数据格式
- **时间连续性验证**: 确保分段之间时间连续无间隙
- **索引文件验证**: 检查分段索引的完整性和准确性
- **可视化对比**: 显示滤波前后的时域和频域对比，包含分段标记
- **统计信息**: 提供分段处理的详细统计信息
- **标注对应分析**: 分析标注文件与分段的对应关系

## 使用方法

### 快速开始
1. 将音频文件和对应的标注文件放入 `1、Raw data/` 文件夹
2. 在 MATLAB 中运行：
   ```matlab
   audio_preprocessing()
   ```
3. 分段处理后的数据将保存在 `2、Processed data/` 文件夹中

### 测试和验证
运行测试脚本验证分段处理结果：
```matlab
test_preprocessing()
```



### 分段处理说明
- 音频文件将按60秒自动分段
- 每个分段生成独立的timetable文件
- 变量命名包含分段信息：`tt1_seg001`, `tt1_seg002`, `tt2_seg001` 等
- 分段索引文件记录完整的时间对应关系
- 支持不足60秒的最后分段处理

## 输出文件说明

### 分段数据文件
- **文件名格式**: `原文件名_segment_001_tt.mat`, `原文件名_segment_002_tt.mat`, ...
- **内容**: 每个文件包含 `tt1_seg001`, `tt2_seg001`, ... 等 timetable 变量（根据音频通道数和分段编号）
- **变量命名**: `tt{通道号}_seg{分段号}` 格式，如 `tt1_seg001`, `tt1_seg002`
- **格式**: MATLAB timetable，包含采样率信息
- **时长**: 每个分段最多60秒（最后一段可能不足60秒）

### 分段索引文件
- **文件名格式**: `原文件名_segments_index.mat`
- **内容**: 包含 `segmentIndex` 结构体，记录所有分段的时间信息和文件名对应关系
- **用途**: 用于重建完整时间轴和分段间的对应关系

### 处理信息文件
- **文件名格式**: `原文件名_tt_info.mat`
- **内容**: 包含处理参数、分段信息和元数据

## 标注文件格式
标注文件应为制表符分隔的文本文件，格式如下：
```
开始时间(秒)    结束时间(秒)    类别标签
0.000000       1.399535       MB
1.562815       2.075978       CRS
2.869048       3.102304       SB
```

支持的类别标签：
- **MB**: 正常肠鸣音
- **CRS**: 异常肠鸣音
- **SB**: 静音段

## 技术参数

### 分段处理设置
- **分段长度**: 60秒
- **处理方式**: 顺序分段，无重叠
- **最后分段**: 自动处理不足60秒的剩余部分

### 滤波器设置
- **类型**: 带通滤波器
- **频率范围**: 100-800 Hz
- **实现**: MATLAB `bandpass()` 函数

### 数据格式
- **输入**: 标准音频格式 (WAV, MP3, M4A, FLAC)
- **输出**: 分段MATLAB timetable 格式
- **采样率**: 保持原始采样率不变

## 注意事项

1. **工作环境**: 所有脚本都会自动清理工作环境（clear all; clc; close all）
2. **文件路径**: 确保音频文件和标注文件在正确的文件夹中
3. **文件命名**: 建议音频文件和标注文件使用相同的基础文件名
4. **内存使用**: 分段处理减少了内存占用，适合处理大文件
5. **采样率**: 确保采样率足够高以支持 800Hz 的高频截止
6. **分段长度**: 60秒分段适合大多数标注函数，可根据需要调整
7. **跨分段标注**: 注意处理跨越分段边界的标注事件

## 故障排除

### 常见问题
1. **"输入文件夹不存在"**: 检查文件夹路径和名称
2. **"未找到音频文件"**: 确认音频文件格式和位置
3. **滤波失败**: 检查音频文件是否损坏或采样率过低
4. **分段时间不连续**: 检查原始音频文件的完整性

### 调试建议
1. 运行 `test_preprocessing()` 进行全面测试
2. 检查 MATLAB 命令窗口的详细输出信息
3. 验证生成的分段可视化图形
4. 检查分段索引文件的时间连续性

## 输出文件结构示例
```
2、Processed data/
├── record_080524001_1_segment_001_tt.mat  # 第1个60秒分段 (包含 tt1_seg001)
├── record_080524001_1_segment_002_tt.mat  # 第2个60秒分段 (包含 tt1_seg002)
├── record_080524001_1_segment_003_tt.mat  # 第3个60秒分段 (包含 tt1_seg003)
├── record_080524001_1_segments_index.mat  # 分段索引文件
└── record_080524001_1_tt_info.mat         # 处理信息文件
```

**变量命名说明**：
- 每个分段文件的变量名格式：`tt{通道号}_seg{分段号}`
- 单通道音频：`tt1_seg001`, `tt1_seg002`, `tt1_seg003`, ...
- 双通道音频：`tt1_seg001`, `tt2_seg001`, `tt1_seg002`, `tt2_seg002`, ...

这样的命名方式让您在双击打开.mat文件时，能够清楚地看到是哪个通道的哪个分段数据。

## 后续步骤
1. 使用生成的分段 timetable 数据测试标注函数
2. 验证跨分段标注的处理策略
3. 根据标注函数要求调整分段参数
4. 进行分段标注准确性评估
5. 考虑是否需要分段重叠处理
