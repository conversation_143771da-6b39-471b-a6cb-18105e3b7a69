clear all;
clc;
close all;

splitVideo('input', 'output')

function splitVideo(inputFolder, outputFolder)
    % splitVideo - 将输入文件夹中的视频按特定模式分割（16个24秒片段+1个5秒片段循环）
    % 参数:
    %   inputFolder - 输入视频所在的文件夹路径，例如 'input'
    %   outputFolder - 输出视频保存的文件夹路径，例如 'output'
    %
    % 示例:
    %   splitVideo('input', 'output')

    % 确保输入输出文件夹存在
    if ~exist(inputFolder, 'dir')
        error('输入文件夹不存在: %s', inputFolder);
    end

    % 如果输出文件夹不存在，则创建
    if ~exist(outputFolder, 'dir')
        mkdir(outputFolder);
        fprintf('已创建输出文件夹: %s\n', outputFolder);
    end

    % 获取输入文件夹中的所有视频文件
    videoFiles = dir(fullfile(inputFolder, '*.mp4')); % 可以根据需要添加其他视频格式
    videoFiles = [videoFiles; dir(fullfile(inputFolder, '*.avi'))];
    videoFiles = [videoFiles; dir(fullfile(inputFolder, '*.mov'))];

    if isempty(videoFiles)
        error('在输入文件夹中未找到视频文件');
    end

    % 处理每个视频文件
    for fileIdx = 1:length(videoFiles)
        currentVideo = videoFiles(fileIdx);
        fprintf('\n开始处理视频 %d/%d: %s\n', fileIdx, length(videoFiles), currentVideo.name);

        % 构建完整的输入文件路径
        inputVideoPath = fullfile(inputFolder, currentVideo.name);

        try
            % 创建视频读取对象
            videoReader = VideoReader(inputVideoPath);

            % 显示视频总时长
            fprintf('视频总时长：%.2f秒\n', videoReader.Duration);

            % 询问是否需要删除视频前段
            answer = input('是否需要删除视频前段？(y/n): ', 's');
            startTime = 0;  % 默认从开始处理

            if strcmpi(answer, 'y')
                while true
                    endCutTime = input('请输入要删除的结束时间点（秒）: ');
                    if endCutTime >= 0 && endCutTime < videoReader.Duration
                        startTime = endCutTime;
                        break;
                    else
                        fprintf('输入的时间点无效，请输入0到%.2f之间的数值\n', videoReader.Duration);
                    end
                end
                fprintf('将从%.2f秒处开始处理视频\n', startTime);
            end

            % 重新定位视频读取起始点
            videoReader.CurrentTime = startTime;

            % 获取视频信息
            frameRate = videoReader.FrameRate;
            remainingDuration = videoReader.Duration - startTime;

            % 设置片段持续时间
            normalSegmentDuration = 24; % 常规片段时长（秒）
            shortSegmentDuration = 5;   % 短片段时长（秒）
            segmentsPerGroup = 17;      % 每组片段数量（16个24秒片段+1个5秒片段）

            % 计算每种片段需要的帧数
            normalFramesPerSegment = round(normalSegmentDuration * frameRate);
            shortFramesPerSegment = round(shortSegmentDuration * frameRate);

            % 获取输入文件名（不含扩展名）
            [~, fileName, ~] = fileparts(currentVideo.name);

            % 初始化计数器
            groupIndex = 1;  % 组索引
            segmentInGroupIndex = 1;  % 组内片段索引

            while hasFrame(videoReader)
                % 确定当前片段的帧数
                if segmentInGroupIndex <= 16
                    currentSegmentFrames = normalFramesPerSegment;
                    segmentDuration = normalSegmentDuration;
                else
                    currentSegmentFrames = shortFramesPerSegment;
                    segmentDuration = shortSegmentDuration;
                end

                % 构建输出文件名
                outputFileName = fullfile(outputFolder, sprintf('data%d_%d.mp4', groupIndex, segmentInGroupIndex));

                % 创建视频写入对象
                videoWriter = VideoWriter(outputFileName, 'MPEG-4');
                videoWriter.FrameRate = frameRate;
                videoWriter.Quality = 95;
                open(videoWriter);

                % 读取并写入当前片段的所有帧
                framesWritten = 0;
                while framesWritten < currentSegmentFrames && hasFrame(videoReader)
                    frame = readFrame(videoReader);
                    writeVideo(videoWriter, frame);
                    framesWritten = framesWritten + 1;

                    % 显示进度
                    if mod(framesWritten, 30) == 0
                        fprintf('\r组 %d, 片段 %d 进度: %.1f%%', groupIndex, segmentInGroupIndex, ...
                            (framesWritten / currentSegmentFrames) * 100);
                    end
                end

                % 关闭视频写入对象
                close(videoWriter);
                fprintf('\n完成片段 data%d_%d (%.1f秒)\n', groupIndex, segmentInGroupIndex, segmentDuration);

                % 更新计数器
                if segmentInGroupIndex == segmentsPerGroup
                    groupIndex = groupIndex + 1;
                    segmentInGroupIndex = 1;
                else
                    segmentInGroupIndex = segmentInGroupIndex + 1;
                end

                % 如果剩余帧数不足以构成一个完整片段，则退出
                if videoReader.CurrentTime >= videoReader.Duration
                    break;
                end
            end

            % 显示当前视频的处理统计信息
            fprintf('\n视频 %s 处理完成！\n', currentVideo.name);
            if startTime > 0
                fprintf('已删除开头 %.2f 秒\n', startTime);
            end
            fprintf('处理总时长: %.2f秒\n', remainingDuration);
            fprintf('已生成 %d 组数据，最后一组包含 %d 个片段\n', ...
                groupIndex - 1 + (segmentInGroupIndex > 1), segmentInGroupIndex - 1);

        catch ME
            fprintf('\n处理视频 %s 时出错:\n%s\n', currentVideo.name, ME.message);
            continue; % 继续处理下一个视频
        end
    end

    fprintf('\n所有视频处理完成！\n');
end
