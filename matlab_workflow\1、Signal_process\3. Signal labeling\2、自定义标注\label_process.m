%% 信号数据标注处理程序
% 功能描述：根据标注文件中的时间范围信息，从原始信号数据中提取对应片段并分类保存
%
% 输入说明：标注文件(.mat)包含ROILimits时间范围和Value标签值；原始信号数据文件包含tt1变量
% 输出说明：在output文件夹下按原文件名创建子文件夹，提取的数据片段保存为独立.mat文件
%
% 处理步骤：
% 1. 加载标注文件，提取文件路径和标签信息表
% 2. 创建输出目录结构，为每个原文件建立子文件夹
% 3. 遍历标签表，根据ROILimits时间范围提取信号片段
% 4. 按"原文件名_标签值_序号.mat"格式保存提取的数据
% 5. 输出处理完成信息
%
% 应用场景：信号分析中需要根据人工标注的时间段提取特定类型的信号片段进行后续分析
%
% 注意事项：确保标注文件格式正确，原始数据包含tt1变量且为timetable格式

clear all;
clc;
close all;


% 加载 labeledSignalSet 文件
load('上午第二次.mat'); % 假设文件名为 label1.mat

% 获取 Labels
labels = ls.Labels;

% 提取文件路径列和 BS 表
filePaths = labels.Row; % 提取包含文件路径的列 (请确认列名是否为 `BS`)
bsTables = labels{:, 1}; % 提取第二列 BS 的 table


% 获取当前目录
outputDir = fullfile(pwd, 'output');
if ~exist(outputDir, 'dir')
    mkdir(outputDir); % 创建 output 文件夹
end

% 遍历每一行，提取并处理 bsTables
for i = 1:numel(bsTables)
    % 当前文件路径
    currentFilePath = filePaths{i};
    % 当前的 BS 表
    currentBsTable = bsTables{i};

    % 提取文件名，不含路径和扩展名
    [~, fileName, ~] = fileparts(currentFilePath);

    % 为当前文件创建子文件夹
    subDir = fullfile(outputDir, fileName);
    if ~exist(subDir, 'dir')
        mkdir(subDir); % 创建子文件夹
    end

    % 验证当前 BS 表是否为 table
    if istable(currentBsTable)
        % 遍历当前 BS 表的每一行
        for j = 1:height(currentBsTable)
            % 提取当前行的 ROIlimits 和 Value
            roiLimits = currentBsTable.ROILimits(j, :); % 起始时间和结束时间
            labelValue = currentBsTable.Value(j); % 标签值（从元胞数组提取）

            % 显示提取的信息
            fprintf('文件路径: %s\n', currentFilePath);
            fprintf('起始时间: %.4f, 结束时间: %.4f, 标签: %s\n', ...
                roiLimits(1), roiLimits(2), labelValue);

            % 加载对应的信号数据文件，提取 tt1
            load(currentFilePath, 'tt1'); % 只加载 tt1

            % 根据 ROIlimits 提取时间范围内的数据
            % 假设 tt1 是 timetable 格式
            startTime = roiLimits(1); % 起始时间
            endTime = roiLimits(2); % 结束时间
            extractedData = tt1(timerange(seconds(startTime), seconds(endTime)), :);

            % 构造新的文件名
            newFileName = sprintf('%s_%s_%d.mat', fileName, labelValue, j);

            % 保存文件到对应的子文件夹
            save(fullfile(subDir, newFileName), 'extractedData');
        end
    else
        warning('第 %d 行的 BS 表不是有效的 table，跳过处理。', i);
    end
end

disp('所有 BS 表数据提取完成，并保存到 output 文件夹的对应子目录中。');



