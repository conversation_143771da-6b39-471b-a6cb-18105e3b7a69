

%% 肠鸣音信号自动标注函数（优化版）
% 功能描述：基于频谱分析自动检测和分类肠鸣音信号中的SB、MB、CRS、HS事件
% 输入说明：x-信号数据矩阵，t-时间向量，可选采样率参数(默认2570Hz)
% 输出说明：labelVals-事件类型标签数组，labelLocs-事件时间位置矩阵[起始时间,结束时间]
% 处理步骤：
%   1. 计算信号频谱图并提取100-800Hz频段强度
%   2. 基于自适应阈值检测事件区域
%   3. 分类标准：
%      - SB: 10-30ms，脉冲特征，峰值约400Hz
%      - MB: 40-1500ms，多个SB序列，间隙5-50ms
%      - CRS: 200-4000ms，连续无间隙，高频特征
%      - HS: 50-1500ms，3-4个谐波成分，基频约200Hz
%   4. 优化噪音过滤和事件合并策略
% 应用场景：用于肠鸣音信号的自动化标注和临床分析

function [labelVals,labelLocs] = label(x,t,parentLabelVal,parentLabelLoc,varargin)
    % 初始化输出变量为空数组
    labelVals = string([]);  % 使用空的字符串数组
    labelLocs = zeros(0,2); % 改为0行2列的矩阵

    % 设置采样频率
    if nargin < 5
        Fs = 2570;
    else
        Fs = varargin{1};
    end

    % 显示输入信号的信息
    disp(['输入信号大小: ', num2str(size(x))]);
    disp(['采样率: ', num2str(Fs)]);

    % 肠鸣音标记逻辑
    for kj = 1:size(x,2)
        % 计算频谱图
        w = 0.05; % 设置窗口宽度（秒）
        win = round(Fs*w); % 计算窗口大小（样本数）
        ov = round(Fs*w*0.9); % 计算重叠部分（样本数）
        nfft = round(Fs*0.5); % 设置FFT点数
        [S_C, F_C, T_C, P_C] = spectrogram(x(:,kj), win, ov, nfft, Fs);

        % 计算声音强度（固定100-800Hz频段）
        % 频率分辨率 = Fs/nfft ≈ 2.57Hz
        % 100Hz对应索引 ≈ 39
        % 800Hz对应索引 ≈ 311
        freq_range = 39:311;  % 对应约100-800Hz
        I_BowelSound = sum(P_C(freq_range,:));

        % 显示频率和强度信息
        disp(['频率范围: ', num2str(F_C(freq_range(1))), 'Hz 到 ', num2str(F_C(freq_range(end))), 'Hz']);
        disp(['最大强度: ', num2str(max(I_BowelSound))]);
        disp(['最小强度: ', num2str(min(I_BowelSound))]);

        % 【步骤1优化】自适应阈值设置
        signal_std = std(I_BowelSound);
        signal_mean = mean(I_BowelSound);
        threshold = signal_mean + 2 * signal_std;  % 基于信号统计特性
        disp(['自适应阈值: ', num2str(threshold)]);
        disp(['信号均值: ', num2str(signal_mean), '，标准差: ', num2str(signal_std)]);

        % 找到大于阈值的区域
        above_threshold = I_BowelSound > threshold;

        % 找到上升沿和下降沿
        rising_edges = find(diff([0, above_threshold, 0]) == 1);
        falling_edges = find(diff([0, above_threshold, 0]) == -1) - 1;

        % 计算3ms对应的样本数
        extension_samples = round(0.003 * Fs);

        % 将每个事件的边界向前后延伸3ms
        extended_rising_edges = max(1, rising_edges - extension_samples);
        extended_falling_edges = min(length(T_C), falling_edges + extension_samples);

        % 初始化标签数组
        num_events = length(rising_edges);
        event_types = cell(num_events, 1);
        event_starts = zeros(num_events, 1);
        event_ends = zeros(num_events, 1);
        event_intensities = zeros(num_events, 1);

        % 【步骤1优化】首先检测所有可能的事件并进行初步分类
        for i = 1:num_events
            % 计算事件持续时间（毫秒），使用延伸后的边界
            duration_ms = (T_C(extended_falling_edges(i)) - T_C(extended_rising_edges(i))) * 1000;

            % 计算事件的平均强度，使用原始边界计算强度
            event_range = rising_edges(i):falling_edges(i);
            event_intensities(i) = mean(I_BowelSound(event_range));

            % 记录事件的起止时间，使用延伸后的边界
            event_starts(i) = T_C(extended_rising_edges(i));
            event_ends(i) = T_C(extended_falling_edges(i));

            % 【步骤1优化】精确的SB检测逻辑
            if duration_ms < 1  % 过滤掉太短的事件
                event_types{i} = '';
            elseif duration_ms >= 10 && duration_ms <= 30
                % 验证是否为典型的脉冲特征
                if event_intensities(i) > threshold * 1.2  % 强度足够高
                    event_types{i} = 'SB';
                    disp(['检测到SB事件 ', num2str(i), ':']);
                    disp(['  持续时间: ', num2str(duration_ms), ' ms']);
                    disp(['  强度: ', num2str(event_intensities(i))]);
                    disp(['  时间范围: ', num2str(event_starts(i)), 's - ', num2str(event_ends(i)), 's']);
                else
                    event_types{i} = '';  % 过滤掉弱信号
                end
            elseif duration_ms >= 200 && duration_ms <= 4000
                % 【步骤3优化】CRS候选事件，需要进一步验证连续性
                event_types{i} = 'CRS_candidate';
            elseif duration_ms > 30 && duration_ms < 200
                % 可能的MB组件或HS候选
                event_types{i} = 'SB';  % 暂时标记为SB，后续MB检测会处理
            else
                event_types{i} = '';  % 过长或其他情况
            end
        end

        % 【步骤2重构】新的MB检测逻辑：检查时间相近但有间隙的SB序列
        i = 1;
        while i <= num_events
            if strcmp(event_types{i}, 'SB')
                % 寻找后续的SB事件
                mb_sequence = i;
                j = i + 1;
                
                while j <= num_events && strcmp(event_types{j}, 'SB')
                    % 计算间隙时间
                    gap_time = event_starts(j) - event_ends(mb_sequence(end));
                    
                    % 间隙应该在5-50ms之间（短暂不规则间隙）
                    if gap_time >= 0.005 && gap_time <= 0.05
                        mb_sequence = [mb_sequence, j];
                    else
                        break;  % 间隙过大，停止序列
                    end
                    j = j + 1;
                end
                
                % 如果找到2-5个SB组成的序列
                if length(mb_sequence) >= 2 && length(mb_sequence) <= 5
                    % 验证总持续时间是否在40-1500ms范围内
                    total_duration = (event_ends(mb_sequence(end)) - event_starts(mb_sequence(1))) * 1000;
                    
                    if total_duration >= 40 && total_duration <= 1500
                        % 将序列标记为MB组件
                        for idx = mb_sequence
                            event_types{idx} = 'MB_component';
                        end
                        disp(['检测到MB序列：事件 ', num2str(mb_sequence), '，总时长: ', num2str(total_duration), 'ms']);
                    end
                end
                
                i = j;  % 跳过已处理的事件
            else
                i = i + 1;
            end
        end

        % 【步骤3优化】CRS连续性验证
        for i = 1:num_events
            if strcmp(event_types{i}, 'CRS_candidate')
                duration_ms = (T_C(extended_falling_edges(i)) - T_C(extended_rising_edges(i))) * 1000;
                
                % 分析事件内部的连续性
                event_range = rising_edges(i):falling_edges(i);
                event_intensities_sequence = I_BowelSound(event_range);
                
                % 计算强度变化的变异系数，CRS应该相对稳定
                intensity_cv = std(event_intensities_sequence) / mean(event_intensities_sequence);
                
                % 检查是否有显著的静默间隙
                silence_threshold = threshold * 0.3;
                silence_gaps = event_intensities_sequence < silence_threshold;
                max_continuous_silence = 0;
                current_silence = 0;
                
                for j = 1:length(silence_gaps)
                    if silence_gaps(j)
                        current_silence = current_silence + 1;
                        max_continuous_silence = max(max_continuous_silence, current_silence);
                    else
                        current_silence = 0;
                    end
                end
                
                % CRS判断条件：强度变化系数适中，无显著静默间隙
                max_silence_ms = max_continuous_silence * (T_C(2) - T_C(1)) * 1000;
                
                if intensity_cv < 0.8 && max_silence_ms < 20  % 连续性好，无长间隙
                    event_types{i} = 'CRS';
                    disp(['确认CRS事件：持续时间 ', num2str(duration_ms), 'ms，连续性良好']);
                    disp(['  强度变异系数: ', num2str(intensity_cv)]);
                    disp(['  最大静默间隙: ', num2str(max_silence_ms), 'ms']);
                else
                    event_types{i} = '';  % 不符合CRS特征
                    disp(['排除CRS候选：间隙过大或变化过剧烈']);
                end
            end
        end

        % 【步骤4新增】HS (谐波音) 检测
        for i = 1:num_events
            if strcmp(event_types{i}, 'SB') || isempty(event_types{i}) || strcmp(event_types{i}, '')
                % 对未分类或可疑事件进行谐波分析
                event_range = rising_edges(i):falling_edges(i);
                duration_ms = (T_C(falling_edges(i)) - T_C(rising_edges(i))) * 1000;
                
                % HS持续时间检查
                if duration_ms >= 50 && duration_ms <= 1500
                    % 分析该事件的频谱特征
                    event_spectrum = mean(P_C(:, event_range), 2);
                    
                    % 寻找频谱峰值
                    [peaks, peak_locs] = findpeaks(event_spectrum, 'MinPeakHeight', max(event_spectrum) * 0.15, ...
                                                 'MinPeakDistance', round(0.05 * length(event_spectrum)));
                    
                    % 将峰值位置转换为频率
                    if ~isempty(peaks)
                        peak_frequencies = F_C(peak_locs);
                        
                        % 过滤100-800Hz范围内的峰值
                        valid_peaks = peak_frequencies >= 100 & peak_frequencies <= 800;
                        peak_frequencies = peak_frequencies(valid_peaks);
                        peaks = peaks(valid_peaks);
                        
                        % HS判断：需要3-4个清晰峰值
                        if length(peak_frequencies) >= 3 && length(peak_frequencies) <= 4
                            % 检查是否为谐波关系
                            [peaks_sorted, sort_idx] = sort(peaks, 'descend');
                            freq_sorted = peak_frequencies(sort_idx);
                            
                            % 假设最强峰值为基频或其谐波
                            best_fundamental = 0;
                            max_harmonic_matches = 0;
                            
                            for base_freq = 100:5:300  % 搜索可能的基频
                                harmonic_matches = 0;
                                for freq = freq_sorted
                                    % 检查是否为基频的整数倍（允许8%误差）
                                    harmonic_ratio = freq / base_freq;
                                    if abs(harmonic_ratio - round(harmonic_ratio)) < 0.08
                                        harmonic_matches = harmonic_matches + 1;
                                    end
                                end
                                
                                if harmonic_matches > max_harmonic_matches
                                    max_harmonic_matches = harmonic_matches;
                                    best_fundamental = base_freq;
                                end
                            end
                            
                            % 如果找到良好的谐波关系
                            if max_harmonic_matches >= 3
                                event_types{i} = 'HS';
                                disp(['检测到HS事件 ', num2str(i), ':']);
                                disp(['  持续时间: ', num2str(duration_ms), ' ms']);
                                disp(['  估计基频: ', num2str(best_fundamental), ' Hz']);
                                disp(['  谐波数: ', num2str(max_harmonic_matches)]);
                            end
                        end
                    end
                end
            end
        end

        % 转换为输出格式
        if ~isempty(event_types)
            % 创建临时数组存储有效的标签和位置
            valid_labels = {};
            valid_locs = zeros(0,2);

            % 遍历所有事件
            for i = 1:num_events
                if ~isempty(event_types{i}) && ~strcmp(event_types{i}, '') && ~strcmp(event_types{i}, 'CRS_candidate')
                    valid_labels{end+1} = event_types{i};
                    valid_locs(end+1,:) = [event_starts(i), event_ends(i)];
                end
            end

            % 转换为最终输出格式
            if ~isempty(valid_labels)
                labelVals = string(valid_labels(:));
                labelLocs = valid_locs;
            end
        end

        % 【步骤2重构】合并MB序列为单个事件
        if ~isempty(labelVals)
            i = 1;
            while i <= length(labelVals)
                if labelVals(i) == "MB_component"
                    % 找到MB序列的起始
                    mb_start = i;
                    mb_end = i;
                    
                    % 找到整个MB序列
                    while mb_end < length(labelVals) && labelVals(mb_end + 1) == "MB_component"
                        mb_end = mb_end + 1;
                    end
                    
                    % 合并为单个MB事件
                    merged_start = min(labelLocs(mb_start:mb_end,1));
                    merged_end = max(labelLocs(mb_start:mb_end,2));
                    
                    % 保留第一个位置，删除其他
                    labelVals(mb_start) = "MB";
                    labelLocs(mb_start,:) = [merged_start, merged_end];
                    
                    % 标记要删除的组件
                    for j = mb_start + 1:mb_end
                        labelVals(j) = "";
                    end
                    
                    i = mb_end + 1;
                else
                    i = i + 1;
                end
            end

            % 移除空标签
            valid_indices = labelVals ~= "";
            labelVals = labelVals(valid_indices);
            labelLocs = labelLocs(valid_indices,:);
        end

        % 【步骤3优化】改进的噪音过滤策略
        if ~isempty(labelVals)
            window_size = 5; % 5秒窗口
            total_time = max(labelLocs(:,2));
            
            % 创建用于标记要移除的事件的逻辑数组
            events_to_remove = false(size(labelVals));
            
            % 滑动窗口检查
            for t = 0:0.1:total_time-window_size  % 每0.1秒移动一次窗口
                window_start = t;
                window_end = t + window_size;
                
                % 找到窗口内的所有事件
                events_in_window = (labelLocs(:,1) >= window_start & labelLocs(:,1) <= window_end) | ...
                                 (labelLocs(:,2) >= window_start & labelLocs(:,2) <= window_end) | ...
                                 (labelLocs(:,1) <= window_start & labelLocs(:,2) >= window_end);
                
                % 分类型检查密度，CRS和HS事件密度阈值应该更宽松
                sb_mb_count = sum(events_in_window & (labelVals == "SB" | labelVals == "MB"));
                
                % 只有SB/MB过多时才移除，保护CRS和HS事件
                if sb_mb_count > 12  % 降低SB/MB的密度阈值
                    sb_mb_events = events_in_window & (labelVals == "SB" | labelVals == "MB");
                    events_to_remove = events_to_remove | sb_mb_events;
                end
            end
            
            % 移除被标记的事件
            if any(events_to_remove)
                disp(['噪音过滤移除了 ', num2str(sum(events_to_remove)), ' 个高密度SB/MB事件']);
                labelVals = labelVals(~events_to_remove);
                labelLocs = labelLocs(~events_to_remove,:);
            end
        end

        % 显示最终标签信息
        for i = 1:length(labelVals)
            duration_ms = (labelLocs(i,2) - labelLocs(i,1)) * 1000;
            disp(['最终标签 ', num2str(i), ': ', char(labelVals(i)), ...
                ' (', num2str(labelLocs(i,1)), 's - ', num2str(labelLocs(i,2)), 's, ', ...
                num2str(duration_ms), 'ms)']);
        end

        % 显示统计信息
        disp(['=== 检测结果统计 ===']);
        disp(['事件总数: ', num2str(length(labelVals))]);
        if ~isempty(labelVals)
            disp(['SB数量: ', num2str(sum(labelVals == "SB"))]);
            disp(['MB数量: ', num2str(sum(labelVals == "MB"))]);
            disp(['CRS数量: ', num2str(sum(labelVals == "CRS"))]);
            disp(['HS数量: ', num2str(sum(labelVals == "HS"))]);
            
            % 显示持续时间统计
            for event_type = ["SB", "MB", "CRS", "HS"]
                type_indices = labelVals == event_type;
                if any(type_indices)
                    durations = (labelLocs(type_indices, 2) - labelLocs(type_indices, 1)) * 1000;
                    disp([char(event_type), ' 持续时间范围: ', num2str(min(durations), '%.1f'), '-', ...
                          num2str(max(durations), '%.1f'), 'ms，平均: ', num2str(mean(durations), '%.1f'), 'ms']);
                end
            end
        end
    end

    % 如果没有检测到任何标签，确保输出为空
    if isempty(labelVals)
        labelVals = string([]);
        labelLocs = zeros(0,2);
    end
end
























% %% 肠鸣音信号自动标注函数
% % 功能描述：基于频谱分析自动检测和分类肠鸣音信号中的SB、MB、CRS事件
% % 输入说明：x-信号数据矩阵，t-时间向量，可选采样率参数(默认2570Hz)
% % 输出说明：labelVals-事件类型标签数组，labelLocs-事件时间位置矩阵[起始时间,结束时间]
% % 处理步骤：
% %   1. 计算信号频谱图并提取100-800Hz频段强度
% %   2. 基于阈值检测超过0.0001的强度区域
% %   3. 根据持续时间分类：≤30ms为SB，>200ms为CRS
% %   4. 检测重叠SB事件并重新分类为MB
% %   5. 合并相邻MB事件并过滤高密度噪音区域
% % 应用场景：用于肠鸣音信号的自动化标注和临床分析
% 
% function [labelVals,labelLocs] = label(x,t,parentLabelVal,parentLabelLoc,varargin)
%     % 初始化输出变量为空数组
%     labelVals = string([]);  % 使用空的字符串数组
%     labelLocs = zeros(0,2); % 改为0行2列的矩阵
% 
%     % 设置采样频率
%     if nargin < 5
%         Fs = 2570;
%     else
%         Fs = varargin{1};
%     end
% 
%     % 显示输入信号的信息
%     disp(['输入信号大小: ', num2str(size(x))]);
%     disp(['采样率: ', num2str(Fs)]);
% 
%     % 肠鸣音标记逻辑
%     for kj = 1:size(x,2)
%         % 计算频谱图
%         w = 0.05; % 设置窗口宽度（秒）
%         fs = 2570; % 采样频率
%         win = round(fs*w); % 计算窗口大小（样本数）
%         ov = round(fs*w*0.9); % 计算重叠部分（样本数）
%         nfft = round(fs*0.5); % 设置FFT点数
%         [S_C, F_C, T_C, P_C] = spectrogram(x(:,kj), win, ov, nfft, fs);
% 
%         % 计算声音强度
%         % 频率分辨率 = fs/nfft ≈ 2.57Hz
%         % 100Hz对应索引 ≈ 39
%         % 1000Hz对应索引 ≈ 389
%         % 800Hz对应索引 ≈ 311
%         freq_range = 39:311;  % 对应约100-800Hz
%         I_BowelSound = sum(P_C(freq_range,:));
% 
%         % 显示频率和强度信息
%         disp(['频率范围: ', num2str(F_C(freq_range(1))), 'Hz 到 ', num2str(F_C(freq_range(end))), 'Hz']);
%         disp(['最大强度: ', num2str(max(I_BowelSound))]);
%         disp(['最小强度: ', num2str(min(I_BowelSound))]);
% 
%         % 设置阈值
%         threshold = 0.0001;
%         disp(['使用的阈值: ', num2str(threshold)]);
% 
%         % 找到大于阈值的区域
%         above_threshold = I_BowelSound > threshold;
% 
%         % 找到上升沿和下降沿
%         rising_edges = find(diff([0, above_threshold, 0]) == 1);
%         falling_edges = find(diff([0, above_threshold, 0]) == -1) - 1;
% 
%         % 计算3ms对应的样本数
%         extension_samples = round(0.003 * fs);
% 
%         % 将每个事件的边界向前后延伸3ms
%         extended_rising_edges = max(1, rising_edges - extension_samples);
%         extended_falling_edges = min(length(T_C), falling_edges + extension_samples);
% 
%         % 初始化标签数组
%         num_events = length(rising_edges);
%         event_types = cell(num_events, 1);
%         event_starts = zeros(num_events, 1);
%         event_ends = zeros(num_events, 1);
%         event_intensities = zeros(num_events, 1);
% 
%         % 首先标记所有可能的SB事件
%         for i = 1:num_events
%             % 计算事件持续时间（毫秒），使用延伸后的边界
%             duration_ms = (T_C(extended_falling_edges(i)) - T_C(extended_rising_edges(i))) * 1000;
% 
%             % 计算事件的平均强度，使用原始边界计算强度
%             event_range = rising_edges(i):falling_edges(i);
%             event_intensities(i) = mean(I_BowelSound(event_range));
% 
%             % 记录事件的起止时间，使用延伸后的边界
%             event_starts(i) = T_C(extended_rising_edges(i));
%             event_ends(i) = T_C(extended_falling_edges(i));
% 
%             % 初始化事件类型
%             if duration_ms < 1  % 过滤掉太短的事件
%                 event_types{i} = '';
%             elseif duration_ms <= 30
%                 event_types{i} = 'SB';
%                 % 显示SB事件信息
%                 disp(['检测到SB事件 ', num2str(i), ':']);
%                 disp(['  持续时间: ', num2str(duration_ms), ' ms']);
%                 disp(['  强度: ', num2str(event_intensities(i))]);
%                 disp(['  延伸前时间范围: ', num2str(T_C(rising_edges(i))), 's - ', num2str(T_C(falling_edges(i))), 's']);
%                 disp(['  延伸后时间范围: ', num2str(event_starts(i)), 's - ', num2str(event_ends(i)), 's']);
%             elseif duration_ms > 200
%                 event_types{i} = 'CRS';
%             else
%                 event_types{i} = 'SB';  % 暂时标记为SB，后续可能会改为MB
%             end
%         end
% 
%         % 新的MB检测逻辑：检查重叠的SB
%         i = 1;
%         while i <= num_events
%             if strcmp(event_types{i}, 'SB')
%                 % 查找与当前SB重叠的其他SB
%                 overlapping_sb = [];
%                 current_start = event_starts(i);
%                 current_end = event_ends(i);
% 
%                 % 收集所有重叠的SB
%                 for j = 1:num_events
%                     if i ~= j && strcmp(event_types{j}, 'SB')
%                         % 检查是否重叠
%                         if (event_starts(j) <= current_end && event_ends(j) >= current_start)
%                             overlapping_sb = [overlapping_sb, j];
%                         end
%                     end
%                 end
% 
%                 % 如果找到重叠的SB（包括当前SB）
%                 if length(overlapping_sb) >= 1 && length(overlapping_sb) <= 4  % 总数2-5个（当前的+重叠的）
%                     % 将所有重叠的SB标记为MB
%                     event_types{i} = 'MB';
%                     for idx = overlapping_sb
%                         event_types{idx} = 'MB';
%                     end
%                     disp(['检测到MB组：事件 ', num2str(i), ' 与事件 ', num2str(overlapping_sb)]);
%                     i = max(overlapping_sb) + 1;  % 跳过已处理的SB
%                     continue;
%                 end
%             end
%             i = i + 1;
%         end
% 
%         % 转换为输出格式
%         if ~isempty(event_types)
%             % 创建临时数组存储有效的标签和位置
%             valid_labels = {};
%             valid_locs = zeros(0,2);
% 
%             % 遍历所有事件
%             for i = 1:num_events
%                 if ~isempty(event_types{i})
%                     valid_labels{end+1} = event_types{i};
%                     valid_locs(end+1,:) = [event_starts(i), event_ends(i)];
%                 end
%             end
% 
%             % 转换为最终输出格式
%             if ~isempty(valid_labels)
%                 labelVals = string(valid_labels(:));
%                 labelLocs = valid_locs;
%             end
%         end
% 
%         % 合并相邻的MB
%         if ~isempty(labelVals)
%             i = 1;
%             while i < length(labelVals)
%                 if labelVals(i) == "MB"
%                     % 寻找连续的MB
%                     mb_group = i;
%                     j = i + 1;
%                     while j <= length(labelVals) && labelVals(j) == "MB"
%                         % 检查是否相邻（时间间隔小于某个阈值，比如0.5秒）
%                         if labelLocs(j,1) - labelLocs(j-1,2) < 0.5
%                             mb_group = [mb_group, j];
%                         else
%                             break;
%                         end
%                         j = j + 1;
%                     end
% 
%                     % 如果找到2-3个相邻的MB
%                     if length(mb_group) >= 2 && length(mb_group) <= 3
%                         disp(['合并相邻MB：事件 ', num2str(mb_group)]);
%                         % 合并这些MB
%                         merged_start = min(labelLocs(mb_group,1));
%                         merged_end = max(labelLocs(mb_group,2));
% 
%                         % 移除原来的MB
%                         labelVals(mb_group) = "";
%                         % 只保留第一个MB的位置信息
%                         labelLocs(mb_group(1),:) = [merged_start, merged_end];
%                         labelVals(mb_group(1)) = "MB";
% 
%                         % 更新索引
%                         i = j;
%                         continue;
%                     end
%                 end
%                 i = i + 1;
%             end
% 
%             % 移除空标签
%             valid_indices = labelVals ~= "";
%             labelVals = labelVals(valid_indices);
%             labelLocs = labelLocs(valid_indices,:);
%         end
% 
%         % 噪音检测：检查5秒窗口内的事件数量
%         if ~isempty(labelVals)
%             window_size = 5; % 5秒窗口
%             total_time = max(labelLocs(:,2));
% 
%             % 创建用于标记要移除的事件的逻辑数组
%             events_to_remove = false(size(labelVals));
% 
%             % 滑动窗口检查
%             for t = 0:0.1:total_time-window_size  % 每0.1秒移动一次窗口
%                 window_start = t;
%                 window_end = t + window_size;
% 
%                 % 找到窗口内的所有事件
%                 events_in_window = (labelLocs(:,1) >= window_start & labelLocs(:,1) <= window_end) | ...
%                                  (labelLocs(:,2) >= window_start & labelLocs(:,2) <= window_end) | ...
%                                  (labelLocs(:,1) <= window_start & labelLocs(:,2) >= window_end);
% 
%                 % 如果窗口内事件数量超过15，标记要移除的事件
%                 if sum(events_in_window) > 15
%                     events_to_remove = events_to_remove | events_in_window;
%                 end
%             end
% 
%             % 移除被标记的事件
%             if any(events_to_remove)
%                 labelVals = labelVals(~events_to_remove);
%                 labelLocs = labelLocs(~events_to_remove,:);
%             end
%         end
% 
%         % 显示标签信息
%         for i = 1:length(labelVals)
%             disp(['标签 ', num2str(i), ': ', char(labelVals(i)), ...
%                 ' (', num2str(labelLocs(i,1)), 's - ', num2str(labelLocs(i,2)), 's)']);
%         end
% 
%         % 显示统计信息
%         disp(['检测到的事件总数: ', num2str(length(labelVals))]);
%         if ~isempty(labelVals)
%             disp(['SB数量: ', num2str(sum(labelVals == "SB"))]);
%             disp(['MB数量: ', num2str(sum(labelVals == "MB"))]);
%             disp(['CRS数量: ', num2str(sum(labelVals == "CRS"))]);
%         end
%     end
% 
%     % 如果没有检测到任何标签，确保输出为空
%     if isempty(labelVals)
%         labelVals = string([]);
%         labelLocs = zeros(0,2);
%     end
% end
% 
% 
% 
% 
% 
% 
% 
% 
% 
% 
% 
% 
