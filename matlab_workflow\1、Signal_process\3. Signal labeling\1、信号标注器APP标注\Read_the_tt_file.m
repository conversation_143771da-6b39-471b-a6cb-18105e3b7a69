% 清空工作空间并关闭所有图形窗口
clear all;
close all;
clc;

% 加载数据文件 - 让用户选择文件
[filename, pathname] = uigetfile('*.mat', '请选择包含timetable数据的.mat文件');

% 检查用户是否取消了文件选择
if isequal(filename, 0)
    disp('用户取消了文件选择');
    return;
end

% 构建完整的文件路径并加载
fullpath = fullfile(pathname, filename);
fprintf('正在加载文件: %s\n', fullpath);
load(fullpath);

% 检查加载的数据中是否存在tt1
if ~exist('tt1', 'var')
    error('在数据文件中找不到tt1');
end

% 确保tt1是timetable格式
if ~istimetable(tt1)
    error('tt1不是timetable格式');
end

% 从timetable中提取时间和信号数据
time = tt1.Time - tt1.Time(1); % 将时间归零
signal = tt1.Variables; % 假设timetable只有一列数据