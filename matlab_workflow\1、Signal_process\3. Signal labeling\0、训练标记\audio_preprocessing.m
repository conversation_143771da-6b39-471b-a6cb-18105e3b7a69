function audio_preprocessing()
% AUDIO_PREPROCESSING 音频文件预处理函数（支持分段处理和降采样）
%
% 功能描述：
% 1. 读取指定文件夹中的音频文件
% 2. 降采样到2570Hz
% 3. 应用100-800Hz带通滤波器进行降噪处理
% 4. 将长音频按60秒分段处理
% 5. 将每个分段转换为独立的timetable格式文件
% 6. 生成分段索引文件记录时间对应关系
%
% 输入：无（自动处理当前文件夹下的Raw data文件夹）
% 输出：分段处理后的.mat文件保存在2、Processed data文件夹中
%

    % 清理工作环境
    clear all;
    clc;
    close all;


    %% 设置文件路径
    % 获取当前脚本所在目录
    currentDir = fileparts(mfilename('fullpath'));
    
    % 设置输入和输出文件夹路径
    inputFolder = fullfile(currentDir, '1、Raw data');
    outputFolder = fullfile(currentDir, '2、Processed data');
    
    % 检查输入文件夹是否存在
    if ~exist(inputFolder, 'dir')
        error('输入文件夹不存在: %s', inputFolder);
    end
    
    % 创建输出文件夹，如果不存在的话
    if ~exist(outputFolder, 'dir')
        mkdir(outputFolder);
        fprintf('已创建输出文件夹: %s\n', outputFolder);
    end
    
    %% 获取音频文件列表
    % 支持常见的音频格式
    audioExtensions = {'*.wav', '*.mp3', '*.m4a', '*.flac'};
    audioFiles = [];

    % 预先收集所有文件
    allFiles = {};
    for i = 1:length(audioExtensions)
        files = dir(fullfile(inputFolder, audioExtensions{i}));
        if ~isempty(files)
            allFiles{end+1} = files;
        end
    end

    % 合并所有文件
    if ~isempty(allFiles)
        audioFiles = vertcat(allFiles{:});
    end
    
    if isempty(audioFiles)
        error('在输入文件夹中未找到音频文件: %s', inputFolder);
    end
    
    fprintf('找到 %d 个音频文件待处理\n', length(audioFiles));
    
    %% 处理每个音频文件
    for fileIdx = 1:length(audioFiles)
        currentFileName = audioFiles(fileIdx).name;
        currentFilePath = fullfile(inputFolder, currentFileName);
        
        fprintf('\n正在处理文件 %d/%d: %s\n', fileIdx, length(audioFiles), currentFileName);
        
        try
            %% 读取音频文件
            fprintf('  - 读取音频文件...\n');
            [audioData, originalSamplingRate] = audioread(currentFilePath);

            % 显示原始音频文件基本信息
            [numSamples, numChannels] = size(audioData);
            originalDuration = numSamples / originalSamplingRate;

            fprintf('  - 原始音频信息:\n');
            fprintf('    原始采样率: %d Hz\n', originalSamplingRate);
            fprintf('    通道数: %d\n', numChannels);
            fprintf('    时长: %.2f 秒\n', originalDuration);
            fprintf('    原始样本数: %d\n', numSamples);

            %% 降采样处理
            targetSamplingRate = 2570; % 目标采样率
            fprintf('  - 降采样到 %d Hz...\n', targetSamplingRate);

            % 检查是否需要降采样
            if originalSamplingRate ~= targetSamplingRate
                % 对每个通道进行降采样
                audioData_resampled = zeros(ceil(numSamples * targetSamplingRate / originalSamplingRate), numChannels);

                for ch = 1:numChannels
                    audioData_resampled(:, ch) = resample(audioData(:, ch), targetSamplingRate, originalSamplingRate);
                    fprintf('    通道 %d 降采样完成\n', ch);
                end

                % 更新音频数据和参数
                audioData = audioData_resampled;
                samplingRate = targetSamplingRate;
                [numSamples, ~] = size(audioData);
                duration = numSamples / samplingRate;

                fprintf('  - 降采样后音频信息:\n');
                fprintf('    新采样率: %d Hz\n', samplingRate);
                fprintf('    新样本数: %d\n', numSamples);
                fprintf('    时长: %.2f 秒\n', duration);
            else
                samplingRate = originalSamplingRate;
                duration = originalDuration;
                fprintf('    采样率已经是 %d Hz，无需降采样\n', targetSamplingRate);
            end
            
            %% 应用带通滤波器
            fprintf('  - 应用100-800Hz带通滤波器...\n');
            
            % 滤波器参数
            low_freq = 100;   % 带通滤波的低频边界
            high_freq = 800;  % 带通滤波的高频边界
            
            % 检查采样率是否满足滤波要求
            nyquist_freq = samplingRate / 2;
            if high_freq >= nyquist_freq
                warning('高频边界 (%d Hz) 接近或超过奈奎斯特频率 (%.1f Hz)，调整为 %.1f Hz', ...
                    high_freq, nyquist_freq, nyquist_freq * 0.9);
                high_freq = nyquist_freq * 0.9;
            end
            
            % 对每个通道应用带通滤波
            audioData_filtered = zeros(size(audioData));
            
            for ch = 1:numChannels
                try
                    audioData_filtered(:, ch) = bandpass(audioData(:, ch), ...
                        [low_freq high_freq], samplingRate);
                    fprintf('    通道 %d 滤波完成\n', ch);
                catch ME
                    warning('通道 %d 滤波失败: %s', ch, ME.message);
                    % 如果滤波失败，使用原始数据
                    audioData_filtered(:, ch) = audioData(:, ch);
                end
            end
            
            %% 分段处理音频数据
            fprintf('  - 开始分段处理（60秒/段）...\n');

            % 分段参数
            segmentDuration = 60; % 每段60秒
            samplesPerSegment = segmentDuration * samplingRate;
            totalSegments = ceil(numSamples / samplesPerSegment);

            fprintf('    总时长: %.2f 秒\n', duration);
            fprintf('    分段数量: %d\n', totalSegments);

            % 初始化分段索引信息
            segmentIndex = struct();
            segmentIndex.originalFile = currentFileName;
            segmentIndex.originalSamplingRate = originalSamplingRate;
            segmentIndex.targetSamplingRate = targetSamplingRate;
            segmentIndex.finalSamplingRate = samplingRate;
            segmentIndex.resamplingApplied = (originalSamplingRate ~= targetSamplingRate);
            segmentIndex.originalDuration = originalDuration;
            segmentIndex.totalDuration = duration;
            segmentIndex.segmentDuration = segmentDuration;
            segmentIndex.totalSegments = totalSegments;
            segmentIndex.numChannels = numChannels;
            segmentIndex.segments = [];

            [~, name, ~] = fileparts(currentFileName);

            % 处理每个分段
            for segIdx = 1:totalSegments
                fprintf('    处理分段 %d/%d...\n', segIdx, totalSegments);

                % 计算当前分段的样本范围
                startSample = (segIdx - 1) * samplesPerSegment + 1;
                endSample = min(segIdx * samplesPerSegment, numSamples);

                % 计算时间偏移
                startTime = (startSample - 1) / samplingRate;
                endTime = (endSample - 1) / samplingRate;
                actualDuration = endTime - startTime;

                % 提取当前分段的音频数据
                segmentData = audioData_filtered(startSample:endSample, :);

                % 为每个通道创建timetable（包含分段信息）
                saveVars = struct();
                for ch = 1:numChannels
                    ttName = sprintf('tt%d_seg%03d', ch, segIdx);
                    saveVars.(ttName) = timetable(segmentData(:, ch), ...
                        'SampleRate', samplingRate);
                end

                % 生成分段文件名
                segmentFileName = sprintf('%s_segment_%03d_tt.mat', name, segIdx);
                segmentFilePath = fullfile(outputFolder, segmentFileName);

                % 保存分段数据
                save(segmentFilePath, '-struct', 'saveVars');

                % 记录分段信息
                segmentInfo = struct();
                segmentInfo.segmentIndex = segIdx;
                segmentInfo.fileName = segmentFileName;
                segmentInfo.startTime = startTime;
                segmentInfo.endTime = endTime;
                segmentInfo.duration = actualDuration;
                segmentInfo.startSample = startSample;
                segmentInfo.endSample = endSample;
                segmentInfo.numSamples = endSample - startSample + 1;

                segmentIndex.segments = [segmentIndex.segments; segmentInfo];

                fprintf('      时间范围: %.2f - %.2f 秒 (%.2f 秒)\n', ...
                    startTime, endTime, actualDuration);
            end

            % 保存分段索引文件
            indexFileName = fullfile(outputFolder, [name, '_segments_index.mat']);
            save(indexFileName, 'segmentIndex');
            fprintf('  - 分段索引文件已保存: %s\n', indexFileName);

            % 保存处理信息（增加分段和降采样相关信息）
            processingInfo = struct();
            processingInfo.originalFile = currentFileName;
            processingInfo.originalSamplingRate = originalSamplingRate;
            processingInfo.targetSamplingRate = targetSamplingRate;
            processingInfo.finalSamplingRate = samplingRate;
            processingInfo.resamplingApplied = (originalSamplingRate ~= targetSamplingRate);
            processingInfo.numChannels = numChannels;
            processingInfo.originalDuration = originalDuration;
            processingInfo.finalDuration = duration;
            processingInfo.filterLowFreq = low_freq;
            processingInfo.filterHighFreq = high_freq;
            processingInfo.processedTime = datetime('now');
            processingInfo.segmentProcessing = true;
            processingInfo.segmentDuration = segmentDuration;
            processingInfo.totalSegments = totalSegments;

            % 保存处理信息到同名的info文件
            infoFileName = fullfile(outputFolder, [name, '_tt_info.mat']);
            save(infoFileName, 'processingInfo');

            fprintf('  - 分段处理完成，共生成 %d 个分段文件\n', totalSegments);
            
        catch ME
            fprintf('  - 错误: 处理文件 %s 时发生错误\n', currentFileName);
            fprintf('    错误信息: %s\n', ME.message);
            continue;
        end
    end
    
    %% 处理完成总结
    fprintf('\n=== 音频分段预处理完成 ===\n');
    fprintf('处理的音频文件数量: %d\n', length(audioFiles));
    fprintf('输出文件夹: %s\n', outputFolder);

    % 检查输出文件
    segmentFiles = dir(fullfile(outputFolder, '*_segment_*_tt.mat'));
    indexFiles = dir(fullfile(outputFolder, '*_segments_index.mat'));
    infoFiles = dir(fullfile(outputFolder, '*_tt_info.mat'));

    fprintf('成功生成的分段文件数量: %d\n', length(segmentFiles));
    fprintf('生成的索引文件数量: %d\n', length(indexFiles));
    fprintf('生成的信息文件数量: %d\n', length(infoFiles));

    if length(segmentFiles) > 0
        fprintf('\n分段文件统计:\n');
        % 按原始文件分组显示
        uniqueFiles = cell(length(segmentFiles), 1); % 预分配
        uniqueCount = 0;

        for i = 1:length(segmentFiles)
            fileName = segmentFiles(i).name;
            % 提取原始文件名（去掉_segment_XXX_tt.mat部分）
            baseName = regexprep(fileName, '_segment_\d+_tt\.mat$', '');
            if ~ismember(baseName, uniqueFiles(1:uniqueCount))
                uniqueCount = uniqueCount + 1;
                uniqueFiles{uniqueCount} = baseName;
                % 统计该文件的分段数量
                pattern = [baseName, '_segment_*_tt.mat'];
                fileSegments = dir(fullfile(outputFolder, pattern));
                fprintf('  %s: %d 个分段\n', baseName, length(fileSegments));
            end
        end
    end

    fprintf('\n处理参数总结:\n');
    fprintf('目标采样率: 2570 Hz\n');
    fprintf('带通滤波器: 100-800 Hz\n');
    fprintf('分段时长: 60 秒\n');

    fprintf('\n建议下一步:\n');
    fprintf('1. 检查生成的分段timetable数据格式是否正确\n');
    fprintf('2. 验证降采样后的音频质量\n');
    fprintf('3. 验证分段之间的时间连续性\n');
    fprintf('4. 使用分段数据测试标注函数\n');
    fprintf('5. 检查分段索引文件的完整性\n');
    
end
